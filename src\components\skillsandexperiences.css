.skillsandexperiences {
  transition: all 0.5s ease;
  margin: 60px 40px;
  padding: 60px 40px;
  border-radius: 30px;
  background: linear-gradient(135deg,
      var(--theme-button-bg-color),
      rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.skillsandexperiences::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
      rgba(120, 162, 170, 0.1) 0%,
      transparent 50%,
      rgba(121, 203, 218, 0.1) 100%);
  z-index: 0;
}

.skillsandexperiences > h1 {
  text-align: center;
  margin: 0 0 40px 0;
  padding: 0 0 20px 0;
  font-size: 3rem;
  font-weight: 700;
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  color: var(--text-color-a);
  border-bottom: 3px solid var(--border-color);
  position: relative;
  z-index: 1;
  background: linear-gradient(135deg, var(--text-color-a), var(--border-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.skillsandexp {
  margin: 40px 0;
  position: relative;
  z-index: 1;
}

.Experience > h1 {
  color: var(--text-color-a);
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  padding: 20px 0;
  text-align: center;
  border-bottom: 2px solid var(--border-color);
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.Experience > p {
  padding: 15px 0;
  color: var(--text-color-a);
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

.skills {
  background: linear-gradient(135deg, var(--boxes-color), rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 40px 30px;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.skills::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.skills:hover::before {
  opacity: 1;
}

.skills:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
}

.skills > h1 {
  margin: 0 0 30px 0;
  padding: 0 0 15px 0;
  font-weight: 600;
  font-size: 2rem;
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  color: var(--text-color-a);
  border-bottom: 2px solid var(--border-color);
  text-align: center;
  position: relative;
  z-index: 1;
}

.skillsList {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.skillsListSections {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  gap: 10px;
}

.divider {
  height: 3px;
  width: 80%;
  background: linear-gradient(90deg, transparent, var(--border-color), transparent);
  border-radius: 2px;
  margin: 10px 0;
}

.Experience {
  margin-top: 40px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.Experience ul {
  list-style: none;
  margin: 0;
  padding: 0;
  color: var(--text-color-a);
}

.Experience li {
  padding: 12px 0;
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  position: relative;
  padding-left: 25px;
  transition: all 0.3s ease;
}

.Experience li::before {
  content: '▶';
  position: absolute;
  left: 0;
  color: var(--border-color);
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.Experience li:hover {
  transform: translateX(5px);
  color: var(--border-color);
}

.Experience li:hover::before {
  transform: scale(1.2);
}

.Experience li > a {
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  color: #e74c3c;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: rgba(231, 76, 60, 0.1);
}

.Experience li > a:hover {
  background-color: rgba(231, 76, 60, 0.2);
  transform: scale(1.05);
}

@media (max-width: 700px) {
  .skillsandexperiences {
    margin: 40px 20px;
    padding: 40px 20px;
  }

  .skillsandexperiences > h1 {
    font-size: 2.5rem;
    margin-bottom: 30px;
  }

  .skillsListSections {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .skills {
    width: 100%;
    padding: 30px 20px;
  }

  .skills > h1 {
    font-size: 1.8rem;
  }

  .skillsList {
    padding: 10px 0;
    gap: 15px;
  }

  .skillsandexp {
    justify-content: center;
    margin: 0;
  }

  .Experience {
    margin-top: 30px;
    padding: 25px 20px;
  }

  .Experience > h1 {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .skillsandexperiences {
    margin: 20px 10px;
    padding: 30px 15px;
  }

  .skillsandexperiences > h1 {
    font-size: 2rem;
  }

  .skills > h1,
  .Experience > h1 {
    font-size: 1.5rem;
  }
}
