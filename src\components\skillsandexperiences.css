.skillsandexperiences {
  transition: 0.5s;
  margin: 40px;
  padding: 40px;
  border-radius: 40px;
  background-color: var(--theme-button-bg-color);
}

.skillsandexperiences > h1 {
  text-align: center;
  margin: 20px;
  padding: 10px;
  font-size: 40px;
  font-weight: 600;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: var(--text-color-a);
  border-bottom: 2px solid var(--border-color);
}

.skillsandexp {
  margin: 40px;
}

.Experience > h1 {
  color: var(--text-color-a);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
}

.Experience > p {
  padding: 10px;
  color: var(--text-color-a);
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.skills {
  background-color: var(--boxes-color);
  width: 508;
  padding-bottom: 40px;
  border-radius: 15px;
  box-shadow: 10px 10px 5px var(--primary-color);
}

.skills > h1 {
  margin: 30px;
  padding: 20px;
  font-weight: 600;
  font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
    "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
  color: var(--text-color-a);
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.skillsList {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
}

.skillsListSections {
  display: flex;
  flex-direction: row;
}

.divider {
  height: 2px;
  width: 70%;
  background-color: rgb(154, 154, 154);
}

.Experience {
  margin-top: 30px;
}

.Experience ul {
  text-decoration: none;
  list-style: circle;
  margin: 20px;
  color: var(--text-color-a);
}
.Experience li {
  padding: 6px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.Experience li > a {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: blue;
  text-decoration: none;
  font-family: monospace;
  font-size: 14px;
  color: rgb(245, 56, 56);
}

@media (max-width: 700px) {
  .skillsandexperiences {
    margin-left: 10px;
    margin-right: -30px;
  }

  .skillsListSections {
    display: flex;
    flex-direction: column;
  }

  .skills {
    width: 100%;
  }

  .skillsList {
    padding: 20px;
  }

  .skillsandexp {
    justify-content: center;
    margin: 0px;
  }
}
