.codingPlatforms{
    transition: all 0.5s ease;
    margin: 60px 40px;
    padding: 60px 40px;
    border-radius: 30px;
    background: linear-gradient(135deg,
        var(--theme-button-bg-color),
        rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.codingPlatforms::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(120, 162, 170, 0.1) 0%,
        transparent 50%,
        rgba(121, 203, 218, 0.1) 100%);
    z-index: 0;
}

.codingPlatforms > h1{
    text-align: center;
    margin: 0 0 40px 0;
    padding: 0 0 20px 0;
    font-size: 3rem;
    font-weight: 700;
    font-family: 'Inter', 'Segoe UI', Robot<PERSON>, sans-serif;
    color: var(--text-color-a);
    border-bottom: 3px solid var(--border-color);
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, var(--text-color-a), var(--border-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.platformNames{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    justify-content: center;
    position: relative;
    z-index: 1;
    padding: 20px 0;
}

@media (max-width:700px) {
    .codingPlatforms{
        margin: 40px 20px;
        padding: 40px 20px;
    }

    .codingPlatforms > h1{
        font-size: 2.5rem;
        margin-bottom: 30px;
    }

    .platformNames{
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 20px;
        padding: 10px 0;
    }
}

@media (max-width:480px) {
    .codingPlatforms{
        margin: 20px 10px;
        padding: 30px 15px;
    }

    .codingPlatforms > h1{
        font-size: 2rem;
    }

    .platformNames{
        grid-template-columns: 1fr;
        gap: 15px;
    }
}