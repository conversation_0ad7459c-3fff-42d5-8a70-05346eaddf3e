.codingPlatforms{
    transition: 0.5s;
    margin: 40px;
    padding: 40px;
    border-radius:40px ;
    background-color: var(--theme-button-bg-color);
}

.codingPlatforms > h1{
    text-align: center;
    margin: 20px;
    padding: 10px;
    font-size: 40px;
    font-weight: 600;
    font-family:system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--text-color-a);
    border-bottom: 2px solid var(--border-color);
}

.platformNames{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;

}

@media (max-width:700px) {
    .codingPlatforms{
        margin-left: 10px;
        margin-right: -30px;
    }

    .codingPlatforms >h1{
        margin-bottom: 80px;
    }

    .platformNames{
        margin: -10px;
    }
}