import React from 'react';
import './App.css';
import Header from './components/header';
import AboutMe from './components/aboutMe';
import Projects from './components/projects';
import SkillsAndExperiences from './components/skillsandexperiences';
import CodingPlatforms from './components/codingPlatforms';
import Footer from './components/footer';
import ContactMe from './components/contactMe';
import BackToTop from './components/BackToTop';
import ScrollProgress from './components/ScrollProgress';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
function App() {
 
  
  return (
    <Router>
    <div>
      <ScrollProgress />
      <Header />

      <Routes>
        <Route path="/" element={
          <>
            <AboutMe />
            <Projects />
            <SkillsAndExperiences />
            <CodingPlatforms />
          </>
        } />

        <Route path="/contactMe" element={
          <>
            <ContactMe />
          </>
        } />

        <Route path="/Projects" element={
          <>
            <Projects />
          </>
        } />

        <Route path="/Platforms" element={
          <>
            <CodingPlatforms />
          </>
        } />
      </Routes>

      <Footer />
      <BackToTop />
    </div>
    </Router>
  );
}


export default App;