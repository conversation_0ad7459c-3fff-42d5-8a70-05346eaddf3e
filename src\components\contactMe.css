.contactMe {
    max-width: 600px;
    margin: 80px auto;
    padding: 50px;
    background: linear-gradient(135deg, var(--boxes-color), rgba(255, 255, 255, 0.1));
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.8s ease-out;
}

.contactMe::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(120, 162, 170, 0.1) 0%,
        transparent 50%,
        rgba(121, 203, 218, 0.1) 100%);
    z-index: 0;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.contactMe > h1{
    color: var(--text-color-a);
    font-family: 'Inter', '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 3px solid var(--border-color);
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, var(--text-color-a), var(--border-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

  .contactMe form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    position: relative;
    z-index: 1;
  }

  .contactMe label {
    color: var(--text-color-a);
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    margin-bottom: 8px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .contactMe input,
  .contactMe textarea {
    padding: 18px 20px;
    margin-bottom: 5px;
    color: var(--text-color-a);
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    font-size: 1rem;
    outline: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .contactMe input:focus,
  .contactMe textarea:focus {
    border-color: var(--border-color);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.15);
  }

  .contactMe input::placeholder,
  .contactMe textarea::placeholder {
    color: rgba(var(--text-color-a), 0.6);
    transition: all 0.3s ease;
  }

  .contactMe input:focus::placeholder,
  .contactMe textarea:focus::placeholder {
    opacity: 0.8;
    transform: translateY(-2px);
  }

  .contactMe textarea {
    resize: vertical;
    min-height: 120px;
  }

  .contactMe input[type="submit"] {
    background: linear-gradient(135deg, var(--border-color), var(--boxes-color));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--text-color-a);
    border-radius: 50px;
    border: none;
    padding: 18px 40px;
    margin-top: 20px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    align-self: center;
    min-width: 200px;
  }

  .contactMe input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
  }

  .contactMe input[type="submit"]:hover::before {
    left: 100%;
  }

  .contactMe input[type="submit"]:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
  }

  .contactMe input[type="submit"]:active {
    transform: translateY(-1px) scale(1.02);
  }

@media (max-width:700px) {
    .contactMe {
        max-width: 90%;
        margin: 60px auto;
        padding: 40px 30px;
    }

    .contactMe > h1{
        font-size: 2rem;
        margin-bottom: 25px;
    }

    .contactMe form {
        gap: 15px;
    }

    .contactMe input,
    .contactMe textarea {
        padding: 15px 18px;
        font-size: 0.95rem;
    }

    .contactMe input[type="submit"] {
        padding: 16px 35px;
        font-size: 1rem;
        min-width: 180px;
    }
}

@media (max-width:480px) {
    .contactMe {
        margin: 40px 15px;
        padding: 30px 20px;
    }

    .contactMe > h1{
        font-size: 1.8rem;
    }

    .contactMe input,
    .contactMe textarea {
        padding: 14px 16px;
    }

    .contactMe input[type="submit"] {
        padding: 14px 30px;
        min-width: 160px;
    }
}