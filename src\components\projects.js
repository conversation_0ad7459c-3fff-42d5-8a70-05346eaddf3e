import React from "react";
import "./projects.css";
import MyProject from "./MyProjects/myProject";
import NetworkGraphVisualizerProject from "../images/NetworkGraphVisualizerProject.png";
import EMSProject from "../images/EMSProject.png"
import PortfolioWebAppProject from "../images/portfolio_web_app_project.jpg"
import whatappChatProject from "../images/whatsappChatProject.png"

function projects() {
  return (
    <div className="projects">
      <h1>Projects Portfolio</h1>
      <div className="project_list">
        <a href="#">
          <MyProject
            img={PortfolioWebAppProject}
            topic="Portfolio Web App"
            domain="Web Application"
          ></MyProject>
        </a>
        <a href="https://graph-network-visualizer.netlify.app/" target="_blank">
          <MyProject
            img={NetworkGraphVisualizerProject}
            topic="Network Graph Visualizer"
            domain="Web Application"
          ></MyProject>
        </a>
        <a href="https://github.com/kushalyadav2/Employee-Management-System" target="_blank">
          <MyProject
            img={EMSProject}
            topic="Employees Task Management"
            domain="Web Application"
          ></MyProject>
        </a>
        <a href="https://whatsapp-chat-analysis01.streamlit.app/" target="_blank">
          <MyProject
            img={whatappChatProject}
            topic="WhatsApp Chat Analysis"
            domain="Web Application"
          ></MyProject>
        </a>
      </div>
    </div>
  );
}

export default projects;
