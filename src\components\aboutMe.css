.aboutMe {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  padding: 80px 40px;
  background: linear-gradient(135deg,
    rgba(120, 162, 170, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(121, 203, 218, 0.1) 100%);
  position: relative;
  overflow: hidden;
}

.aboutMe::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(120, 162, 170, 0.1) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.details {
  width: 55%;
  color: var(--text-color-a);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin-left: 40px;
  z-index: 1;
  position: relative;
}

.details > h1 {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 20px;
  background: linear-gradient(135deg, var(--text-color-a), var(--border-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: slideInLeft 1s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.details > h3 {
  padding: 15px 0;
  color: #02685f;
  font-size: 1.5rem;
  font-weight: 500;
  position: relative;
  animation: slideInLeft 1s ease-out 0.2s both;
}

.details > h3::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #02685f, var(--border-color));
  border-radius: 2px;
}

.details > p {
  opacity: 0.8;
  width: 90%;
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 30px;
  animation: slideInLeft 1s ease-out 0.4s both;
}

.details > button {
  background: linear-gradient(135deg, var(--border-color), var(--boxes-color));
  padding: 18px 35px;
  border: none;
  border-radius: 50px;
  margin: 30px 0;
  font-size: 16px;
  color: var(--text-color-a);
  font-weight: 600;
  cursor: pointer;
  font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  animation: slideInLeft 1s ease-out 0.6s both;
}

.details > button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.details > button:hover::before {
  left: 100%;
}

.details > button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.details > button:active {
  transform: translateY(-1px) scale(1.02);
}

.programmerImg {
  background-image: var(--aboutMe-developer-img);
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  transition: all 0.5s ease;
  height: 500px;
  width: 600px;
  margin-right: 40px;
  position: relative;
  z-index: 1;
  animation: updown 3s ease-in-out infinite, slideInRight 1s ease-out;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
}

.programmerImg::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(120, 162, 170, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  z-index: -1;
  animation: pulse 4s ease-in-out infinite;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes updown {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@media (max-width: 700px) {
  .aboutMe {
    display: block;
    padding: 40px 20px;
    min-height: auto;
  }

  .details {
    width: 100%;
    display: block;
    text-align: center;
    margin: 0;
    padding: 20px;
  }

  .details > h1 {
    font-size: 2.5rem;
    margin-bottom: 15px;
  }

  .details > h3 {
    font-size: 1.3rem;
    padding: 10px 0;
  }

  .details > h3::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .details > p {
    width: 100%;
    font-size: 1rem;
    margin-bottom: 25px;
  }

  .details > button {
    margin: 20px auto;
    display: block;
    width: fit-content;
  }

  .programmerImg {
    width: 100%;
    height: 300px;
    margin: 40px 0 0 0;
    background-size: contain;
  }

  .programmerImg::before {
    width: 100%;
    height: 100%;
  }
}
