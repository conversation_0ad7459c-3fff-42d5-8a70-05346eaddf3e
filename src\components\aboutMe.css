.aboutMe {
  display: flex;
  justify-content: center;
  align-items: center;
}
.details {
  width: 60%;
  color: var(--text-color-a);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  margin-left: 40px;
}

.details > h3 {
  padding-top: 10px;
  padding-bottom: 10px;
  color: rgb(2, 104, 96);
}

.details > p {
  opacity: 0.7;
  width: 80%;
}

.details > button {
  background-color: var(--border-color);
  padding: 15px;
  border: 2px solid var(--boxes-color);
  border-radius: 7px;
  margin: 30px;
  font-size: 15px;
  color: var(--text-color-a);
  font-weight: bold;
  cursor: pointer;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  transition: 0.1s;
}

.details > button:hover {
  transform: rotate(5deg);
}

.programmerImg {
  background-image: var(--aboutMe-developer-img);
  background-repeat: no-repeat;
  transition: 0.5s;
  height: 500px;
  width: 600px;
  margin-right: 40px;
  animation: 2s updown ease-in-out infinite;
}

@keyframes updown {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0);
  }
}

@media (max-width: 700px) {
  .aboutMe {
    display: block;
  }

  .details {
    width: 100%;
    display: block;
    text-align: center;
    margin: 20px;
  }

  .details p {
    width: 100%;
  }

  .programmerImg {
    width: 100%;
    margin-top: 40px;
    margin-bottom: -60px;
  }
}
