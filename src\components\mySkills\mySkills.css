.mySkills{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 10px;
    transition: all 0.3s ease;
}

.mySkillsIcon{
    height: 100px;
    width: 100px;
    background-size: 70px 70px;
    background-repeat: no-repeat;
    background-position: center;
    margin: 15px;
    margin-bottom: 10px;
    cursor: pointer;
    border-radius: 15px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.mySkillsIcon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.mySkillsIcon:hover::before {
    width: 120px;
    height: 120px;
}

.mySkillsName{
    margin-bottom: 20px;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    color: var(--text-color-a);
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
}

.mySkills:hover .mySkillsName{
    font-weight: 600;
    transform: translateY(-2px);
}

.mySkillsIcon:hover{
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    background-color: var(--border-color);
    border-color: rgba(255, 255, 255, 0.4);
}


@media (max-width:768px) {
    .mySkills{
        margin: 5px;
    }

    .mySkillsIcon{
        height: 80px;
        width: 80px;
        background-size: 60px 60px;
        margin: 10px;
    }

    .mySkillsName{
        font-size: 0.8rem;
        margin-bottom: 15px;
    }
}

@media (max-width:480px) {
    .mySkillsIcon{
        height: 70px;
        width: 70px;
        background-size: 50px 50px;
        margin: 8px;
    }

    .mySkillsName{
        font-size: 0.75rem;
    }
}