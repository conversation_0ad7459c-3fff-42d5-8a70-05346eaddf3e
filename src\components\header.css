.header{
    display: flex;
    justify-content: space-between;
    margin: 20px;
    
}

.menu{
    display: flex;
}

.menu > button{
    display: none;
    background-color: transparent;
    border: none;
    padding: 10px;
}

.menu-h1{
    text-decoration: none;
    font-size: 18px;
    font-weight: 400;
    font-family: '<PERSON><PERSON>', sans-serif;
    margin-top: 15px;
    margin-left: 25px;
    margin-right: 25px;
    color: var(--text-color-a);
    transition: 0.1s;
}

.menu-h1:hover{
    font-weight: bold;
    transform:scale(1.2);
}


.logo{
    margin-left: 40px;
    height: 70px;
    width: 70px;
    cursor: pointer;
    background-image: url(../images/logo1.jpg);
    background-size: 70px 70px;
    border-radius: 20px;
    box-shadow: 10px 10px 7px var(--theme-button-bg-color);
}

.themeBtn{
    margin-right: 40px;
}

.ThemeButton{
    transition: 0.1s;
    border: none;
    border-radius: 30px;
    background-color: var(--theme-button-bg-color);
    background-image: var(--theme-button-bg-img);
    background-size: var(--theme-button-bg-size);
    background-repeat: no-repeat;
    background-position: center;
    height: 50px;
    width: 50px;
    cursor: pointer;
    box-shadow: 0px 0px 10px ;

}

.MenuButton{
    margin-right: -30px;
    margin-top: 10px;
    background-color: transparent;
    border: none;
    display: none;
}

.MenuButtonIcon{
    color: var(--text-color-a);
    cursor: pointer;
}

@media (max-width:700px) {

    .header{
        padding-bottom: 30px;
    }

    .logo{
        height: 60px;
        width: 60px;
        background-size: 60px 60px;
        margin-left: 10px;
    }

    .themeBtn{
        margin-top: 5px;
        margin-left: 60px;
    }


    .menu{
        display: flex;
        position: fixed;
        align-items: center;
        top: 0;
        padding-top: 80px;
        width: 350px;
        height:100%;
        right: 0;
        z-index: 2;
        flex-direction: column;
        background-color: var(--header-bg-color);
        transition: 0.2s;
    }

    .menu > button{
        display: inline;
        position: absolute;
        right: 20px;
        top: 20px;
    }

    .MenuButton{
        display: flex;
    }

    .show {
        right: 0;
      }
      
    .hide {
        right: -100%;
      }
}