.header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0;
    padding: 20px 40px;
    background: linear-gradient(135deg, var(--header-bg-color), rgba(255, 255, 255, 0.1));
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.menu{
    display: flex;
    align-items: center;
    gap: 10px;
}

.menu > button{
    display: none;
    background-color: transparent;
    border: none;
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.menu > button:hover{
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.05);
}

.menu-h1{
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    padding: 12px 20px;
    margin: 0 5px;
    color: var(--text-color-a);
    border-radius: 25px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.menu-h1::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.menu-h1:hover::before {
    left: 100%;
}

.menu-h1:hover{
    font-weight: 600;
    transform: translateY(-2px);
    background-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}


.logo{
    height: 60px;
    width: 60px;
    cursor: pointer;
    background-image: url(../images/logo1.jpg);
    background-size: cover;
    background-position: center;
    border-radius: 50%;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.logo:hover{
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
    border-color: var(--border-color);
}

.themeBtn{
    display: flex;
    align-items: center;
}

.ThemeButton{
    height: 45px;
    width: 45px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background-image: var(--theme-button-bg-img);
    background-size: var(--theme-button-bg-size);
    background-repeat: no-repeat;
    background-position: center;
    background-color: var(--theme-button-bg-color);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.ThemeButton::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.ThemeButton:hover::before {
    width: 100px;
    height: 100px;
}

.ThemeButton:hover{
    transform: scale(1.1) rotate(180deg);
    border-color: var(--border-color);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.ThemeButton:active{
    transform: scale(0.95) rotate(180deg);
}

.MenuButton{
    background-color: transparent;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px;
    display: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.MenuButton:hover{
    background-color: rgba(255, 255, 255, 0.1);
    border-color: var(--border-color);
    transform: scale(1.05);
}

.MenuButtonIcon{
    color: var(--text-color-a);
    cursor: pointer;
    transition: all 0.3s ease;
}

@media (max-width:700px) {
    .header{
        padding: 15px 20px;
        margin: 0;
    }

    .logo{
        height: 50px;
        width: 50px;
        background-size: cover;
    }

    .themeBtn{
        margin: 0;
    }

    .ThemeButton{
        height: 40px;
        width: 40px;
    }

    .menu{
        display: flex;
        position: fixed;
        align-items: center;
        top: 0;
        padding-top: 100px;
        width: 100%;
        max-width: 350px;
        height: 100vh;
        right: 0;
        z-index: 1001;
        flex-direction: column;
        background: linear-gradient(135deg, var(--header-bg-color), rgba(0, 0, 0, 0.9));
        backdrop-filter: blur(20px);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
    }

    .menu > button{
        display: inline;
        position: absolute;
        right: 20px;
        top: 20px;
        background-color: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 8px;
        transition: all 0.3s ease;
    }

    .menu > button:hover{
        background-color: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
    }

    .menu-h1{
        margin: 15px 0;
        padding: 15px 30px;
        width: 80%;
        text-align: center;
        font-size: 18px;
        border-radius: 15px;
        background-color: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }

    .MenuButton{
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .show {
        right: 0;
        opacity: 1;
        visibility: visible;
    }

    .hide {
        right: -100%;
        opacity: 0;
        visibility: hidden;
    }
}