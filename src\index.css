@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
    --primary-color: #78a2aa;
    --theme-button-bg-img: url(./images/dark-mode-img.png);
    --aboutMe-developer-img: url(./images/developer-img-light.svg);
    --theme-button-bg-size: 25px 25px;
    --theme-button-bg-margin: 5px;
    --theme-button-bg-color: rgba(237, 242, 245, 0.553);
    --text-color-a: rgb(3, 41, 82);
    --border-color: rgba(209, 131, 209, 0.545);
    --boxes-color: rgba(121, 203, 218, 0.9);
    --header-bg-color: rgba(115, 158, 201, 0.975);

    /* Enhanced color palette */
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-bg: rgba(255, 255, 255, 0.1);
    --dark-bg: rgba(0, 0, 0, 0.1);

    /* Typography */
    --font-primary: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;

    /* Border radius */
    --radius-sm: 8px;
    --radius-md: 15px;
    --radius-lg: 25px;
    --radius-xl: 50px;

    /* Shadows */
    --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 15px 35px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
}

:root.dark {
    --primary-color: rgb(2, 30, 47);
    --theme-button-bg-img: url(./images/light-mode-img.png);
    --aboutMe-developer-img: url(./images/developer-img-dark.svg);
    --theme-button-bg-color: rgba(182, 214, 234, 0.073);
    --theme-button-bg-size: 35px 35px;
    --theme-button-bg-margin: 0px;
    --text-color-a: rgb(231, 237, 242);
    --border-color: rgb(95, 65, 114);
    --boxes-color: rgba(22, 94, 125, 0.333);
    --header-bg-color: rgba(32, 49, 75, 0.975);

    /* Enhanced dark mode colors */
    --light-bg: rgba(255, 255, 255, 0.05);
    --dark-bg: rgba(0, 0, 0, 0.3);
}
/* Global Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    margin: 0;
    padding: 0;
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--primary-color), rgba(255, 255, 255, 0.1));
    background-attachment: fixed;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    line-height: 1.6;
    color: var(--text-color-a);
    overflow-x: hidden;
}

/* Smooth scrolling for all elements */
* {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--primary-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--boxes-color);
}

/* Selection styles */
::selection {
    background: var(--border-color);
    color: var(--text-color-a);
}

::-moz-selection {
    background: var(--border-color);
    color: var(--text-color-a);
}

/* Focus styles */
*:focus {
    outline: 2px solid var(--border-color);
    outline-offset: 2px;
}

/* Utility classes */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Mobile optimizations */
@media (max-width: 768px) {
    html {
        font-size: 14px;
    }

    body {
        line-height: 1.5;
    }

    /* Touch-friendly interactions */
    button, a, input, textarea {
        min-height: 44px;
        min-width: 44px;
    }

    /* Improved touch targets */
    .menu-h1 {
        padding: 15px 25px;
        margin: 10px 0;
    }

    /* Better spacing for mobile */
    .fade-in,
    .slide-in-left,
    .slide-in-right {
        animation-duration: 0.6s;
    }
}

@media (max-width: 480px) {
    html {
        font-size: 13px;
    }

    /* Reduce animations on very small screens */
    .fade-in,
    .slide-in-left,
    .slide-in-right {
        animation: none;
    }

    /* Prefer reduced motion */
    @media (prefers-reduced-motion: reduce) {
        *,
        *::before,
        *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
}