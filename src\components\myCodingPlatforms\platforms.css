.platforms{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.platforms::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.platforms:hover::before {
    opacity: 1;
}

.platforms > img{
    height: 120px;
    width: 120px;
    border-radius: 15px;
    transition: all 0.3s ease;
    object-fit: cover;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.platforms > h2{
    font-weight: 500;
    font-size: 1rem;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    padding: 15px 0 0 0;
    margin: 0;
    color: var(--text-color-a);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.platforms:hover{
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
    border-color: var(--border-color);
}

.platforms:hover > img{
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

.platforms:hover > h2{
    color: var(--border-color);
    font-weight: 600;
}

@media (max-width:700px) {
    .platforms{
        padding: 25px 15px;
    }

    .platforms > img{
        height: 100px;
        width: 100px;
    }

    .platforms > h2{
        font-size: 0.9rem;
        padding: 12px 0 0 0;
    }
}

@media (max-width:480px) {
    .platforms{
        padding: 20px 10px;
    }

    .platforms > img{
        height: 80px;
        width: 80px;
    }

    .platforms > h2{
        font-size: 0.8rem;
        padding: 10px 0 0 0;
    }
}