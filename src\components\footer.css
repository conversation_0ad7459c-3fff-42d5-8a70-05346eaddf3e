.footer{
    border-top: 2px solid var(--border-color);
    margin-bottom: 0;
    background: linear-gradient(135deg, var(--theme-button-bg-color), rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 40px;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(120, 162, 170, 0.1) 0%,
        transparent 50%,
        rgba(121, 203, 218, 0.1) 100%);
    z-index: 0;
}

.credits{
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
}

.credits-name:hover{
    color: var(--accent-color);
    transform: scale(1.05);
}

.credits > h3{
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-left: 10px;
    cursor: pointer;
    color: var(--text-color-a);
    font-weight: 400;
    font-family: 'Inter', 'Se<PERSON>e UI', Robot<PERSON>, sans-serif;
    font-size: 1rem;
}

.credits > h3:hover{
    color: var(--border-color);
    transform: translateY(-2px);
}

.social-media{
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    color: var(--text-color-a);
    position: relative;
    z-index: 1;
}

.social-media > a{
    color: var(--text-color-a);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.social-media > a:hover{
    color: var(--accent-color);
    transform: translateY(-3px) scale(1.1);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.footer > h3{
    color: var(--text-color-a);
    font-weight: 400;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

@media (max-width:700px) {
    .footer{
        flex-direction: column;
        gap: 20px;
        padding: 25px 20px;
        text-align: center;
    }

    .credits{
        justify-content: center;
    }

    .social-media{
        justify-content: center;
        gap: 15px;
    }

    .social-media > a{
        padding: 8px;
    }

    .footer > h3{
        text-align: center;
        margin: 0;
    }
}

@media (max-width:480px) {
    .footer{
        padding: 20px 15px;
    }

    .credits > h3,
    .footer > h3{
        font-size: 0.9rem;
    }

    .social-media{
        gap: 12px;
    }
}