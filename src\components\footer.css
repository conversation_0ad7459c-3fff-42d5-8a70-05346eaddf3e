.footer{
    border-top: 1px solid var(--border-color);
    margin-bottom: 0px;
    background-color: var(--theme-button-bg-color);
    display: flex;
    justify-content: space-between;
    padding: 10px;
    padding-left: 40px;
    padding-right: 40px;
}

.credits{
    display: flex;
}

.credits-name:hover{
    color: rgb(236, 46, 46);
    transform: scale(1.05);
}

.credits > h3{
    transition: 0.3s;
    margin-left: 7px;
    cursor: pointer;
    color: var(--text-color-a);
    font-weight: 300;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    font-size: 15px;
}

.social-media{
    display: flex;
    justify-content: space-between;
    width: 250px;
    color: var(--text-color-a);
    
}

.social-media >a{
    color: var(--text-color-a);
    transition: 0.3s;
}

.social-media >a:hover{
    color: rgb(236, 46, 46);
    transform: scale(1.3);
}

.footer > h3{
    color: var(--text-color-a);
    font-weight: 300;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    font-size: 15px;
}

@media (max-width:700px) {
    .footer{
        display: block;
        justify-content: center;
        align-items: center;
        width: 90%;
    }

    .social-media{
        width: 100%;
        
    }

    .footer>h3{
        text-align: center;
    }
}