.projects{
    transition: 0.5s;
    margin: 40px;
    padding: 40px;
    border-radius:40px ;
    background-color: var(--theme-button-bg-color);
}

.projects > h1{
    text-align: center;
    margin: 20px;
    padding: 10px;
    font-size: 40px;
    font-weight: 600;
    font-family:system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--text-color-a);
    border-bottom: 2px solid var(--border-color);

}

.project_list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
}

.project_list > a{
    text-decoration: none;
}

@media (max-width:700px) {
    .projects{
        margin-left: 10px;
        margin-right: -30px;
    }

    .project_list{
        margin: -10px;
    }
}