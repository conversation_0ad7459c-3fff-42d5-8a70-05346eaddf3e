.projects{
    transition: all 0.5s ease;
    margin: 60px 40px;
    padding: 60px 40px;
    border-radius: 30px;
    background: linear-gradient(135deg,
        var(--theme-button-bg-color),
        rgba(255, 255, 255, 0.05));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.projects::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(120, 162, 170, 0.1) 0%,
        transparent 50%,
        rgba(121, 203, 218, 0.1) 100%);
    z-index: 0;
}

.projects > h1{
    text-align: center;
    margin: 0 0 40px 0;
    padding: 0 0 20px 0;
    font-size: 3rem;
    font-weight: 700;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    color: var(--text-color-a);
    border-bottom: 3px solid var(--border-color);
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, var(--text-color-a), var(--border-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.project_list{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    justify-content: center;
    position: relative;
    z-index: 1;
    padding: 20px 0;
}

.project_list > a{
    text-decoration: none;
    transition: all 0.3s ease;
}

.project_list > a:hover{
    transform: translateY(-5px);
}

@media (max-width:700px) {
    .projects{
        margin: 40px 20px;
        padding: 40px 20px;
    }

    .projects > h1{
        font-size: 2.5rem;
        margin-bottom: 30px;
    }

    .project_list{
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 10px 0;
    }
}

@media (max-width:480px) {
    .projects{
        margin: 20px 10px;
        padding: 30px 15px;
    }

    .projects > h1{
        font-size: 2rem;
    }
}