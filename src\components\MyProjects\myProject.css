.myProject{
    background: linear-gradient(135deg, var(--boxes-color), rgba(255, 255, 255, 0.1));
    margin: 0;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

.myProject::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.myProject:hover::before {
    opacity: 1;
}

.myProject:hover{
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
    border-color: var(--border-color);
}

.myProject > img{
    height: 200px;
    width: 100%;
    object-fit: cover;
    border-radius: 15px 15px 0 0;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.myProject:hover > img{
    transform: scale(1.05);
}

.myProject > p{
    color: var(--text-color-a);
    text-align: center;
    padding: 20px 20px 10px 20px;
    font-size: 1.3rem;
    font-weight: 600;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    position: relative;
    z-index: 1;
    line-height: 1.4;
}

.myProject > h4{
    color: var(--text-color-a);
    text-align: center;
    padding: 0 20px 25px 20px;
    font-weight: 400;
    font-size: 1rem;
    font-family: 'Inter', 'Segoe UI', Roboto, sans-serif;
    opacity: 0.8;
    position: relative;
    z-index: 1;
}


@media (max-width:768px) {
    .myProject{
        margin: 0;
    }

    .myProject > img{
        height: 180px;
    }

    .myProject > p{
        font-size: 1.2rem;
        padding: 15px 15px 8px 15px;
    }

    .myProject > h4{
        padding: 0 15px 20px 15px;
        font-size: 0.9rem;
    }
}