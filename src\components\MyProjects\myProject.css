.myProject{
    background-color: var(--boxes-color);
    margin: 60px;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    cursor: pointer;
    transition: 0.5s;
}

.myProject:hover{
    transform: scale(0.9);
}

.myProject > img{
    height: 250px;
    width: 250px;
    padding-top: 30px;
    padding-left: 30px;
    padding-right: 30px;
    border-radius: 10px;

}

.myProject > p{
    color: var(--text-color-a);
    text-align: center;
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
    font-size: 20px;
    font-weight: bold;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}


.myProject > h4{
    color: var(--text-color-a);
    text-align: center;
    padding: 20px;
    font-weight: 200;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}


@media (max-width:768px) {
    .myProject{
        margin: 30px;
    }
}